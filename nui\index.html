<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <script src="https://code.jquery.com/jquery-3.5.0.js"></script>
  </head>
  <style></style>
  <body>
    <div id="app">
      <div class="main-wrapper">
        <div class="main-wrapper-top">
          <div class="playerimageandname">
            <div class="playerimage">
              <img src="./images/logo/playerimg.png" alt="" id="avatar">
              <div class="playername">
                <h2>Hello</h2>
                <h3 id="name">Aiakos Aiakos</h3>
              </div>
            </div>
          </div>
          <div class="garage-top-logo">
            <img src="./images/logo/logo.png" alt="" id="headlogo">
            <h2>GARAGE</h2>
          </div>
          <div class="garage-top-close-button">
            <img src="./images/closebutton.png" alt="">
            <h2>EXIT FROM</h2>
            <h3>GARAGE MENU</h3>
          </div>
        </div>
        <section class="car-category">
          <div class="category-image">
            <i class="fas fa-star"></i>
          </div>
          <div class="car-categorywrapper">
            <div class="car-categoryline"></div>
            <div class="cars">CARS</div>
            <div class="motor">MOTORCYCLES</div>
            <div class="bikes">BIKES</div>
            <div class="air">AIR</div>
            <div class="boat">BOAT</div>
          </div>
        </section>
        <div class="mouse-move"></div>
        <p id="stored2"></p>
        <section class="car-wrapper-motor"></section>
        <section class="car-wrapper-boat"></section>
        <section class="car-wrapper-bikes"></section>
        <section class="car-wrapper-heli"></section>
        <section class="car-wrapper-favorite"></section>
        <section class="car-wrapper"></section>
        <div id="direction"></div>
        <section class="car-box-details">
          <div class="carbox-car-logo-container">
            <div class="carbox-car-logo">
              <img src="./images/logo/bmv.png" id="crlogo" alt="">
            </div>
            <h2 id="model">BMW F30</h2>
            <div class="car-box-text">
         
              <p id="text3"> Lorem ipsum dolor, sit amet consectetur adipisicing elit. Sapiente eum quia ipsum corporis repudiandae, eveniet maiores sequi mollitia ipsa reiciendis exercitationem quibusdam illum rem ullam incidunt, saepe voluptates facilis itaque. </p>
            </div>
          </div>
          <div id="vhclass"></div>
          <p id="plate2"></p>
          <div class="car-box-details2">
            <div class="car-box-details-buttons">
              <h2 id="specs">SPECS</h2>
              <h2 id="transfer">TRANSFER</h2>
              <h2 id="sell">SELL</h2>
              <h2 id="compenents">COMPENENTS</h2>
            </div>
            <div class="carbox-details-wrapper-text">
              <div class="carbox-maxdetails">
                <h2>Max Speed</h2>
                <h3 id="max">320 KM/H</h3> 
              </div>
              <div class="carbox-maxdetails">
                <h2>Acceleration 0-100</h2>
                <h3 id="acce">11.3 Sec</h3>
              </div>
              <div class="carbox-maxdetails">
                <h2>Weight</h2>
                <h3 id="weight">720 Kg</h3>
              </div>
              <div class="carbox-maxdetails">
                <h2>Plate Number</h2>
                <h3 id="plate">Aiakos</h3>
              </div>
              <div class="carbox-transfer">
                <h2>Citizen ID</h2>
                <input type="text" name="" id="identifier" placeholder="ID">
              </div>
              <div class="carbox-transfer">
                <h2>Transfer Vehicle</h2>
                <h3 id="tvehicle"></h3>
              </div>
              <div class="carbox-transfer">
                <h2>Plate Number</h2>
                <h3 id="tplate"></h3>
              </div>
              
              <div class="compenents">
                <h2>Livery</h2>
                <div class="livery">
                  <button id="geri">
                    < </button>
                      <p id="livnumber">0</p>
                      <button id="ileri"> ></button>
                </div>
              </div>
              <div class="compenents">
                <h2>Extras</h2>
                <div class="extras">
                  <button id="geri">
                    < </button>
                      <p id="number">0</p>
                      <button id="ileri"> ></button>
                </div>
                <div class="aply">Apply</div>
              </div>
              <div class="carbox-sell">
                <h2>Valute of car</h2>
                <h3 id="prices">7500</h3>
              </div>
              <div class="carbox-sell">
                <h2>Selling vehicle</h2>
                <h3 id="svehicle"></h3>
              </div>
              <div class="carbox-sell">
                <h2>Plate Number</h2>
                <h3 id="splate"></h3>
              </div>
              <div class="carbox-spawnbutton">
                <div class="spawnbutton" id="spawn">Spawn</div>
                <div class="vale">Vale</div>
                <div class="out">Out</div>
                <div class="transferbutton" id="transferbut">Transfer</div>
                <div class="sellbutton" id="sellbut">Sell</div>
                <div class="repairbutton">Repair</div>
              </div>
            </div>
          </div>
          <section class="car-logo"></section>
      </div>
    </div>
  </body>
  <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/hung1001/font-awesome-pro-v6@18657a9/css/all.min.css">
  <script src="https://code.jquery.com/jquery-3.5.0.js"></script>
  <script src="script.js"></script>
  <script></script>
</html>